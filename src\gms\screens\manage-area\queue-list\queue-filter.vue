<template>
  <FilterPanel :key="modelInfoIds" :hide-field-fuzzy="true" :form-items="formItems" @change="handleGroupChange">
    <template #suffix>
      <slot name="suffix"></slot>
    </template>
  </FilterPanel>
</template>
<script>
import { defineComponent, computed } from "vue";
import { formItemTypes } from "@srpe-fe/uic-el-form-wrapper-vue2";
import { useI18n } from "gms-hooks";
import { containerStatus, loadingStatus } from "gms-constants";
import FilterPanel from "@/gms/components/filter-panel";

export default defineComponent({
  name: "StorageFilter",
  components: { FilterPanel },
  props: {
    options: { type: Object, default: () => {} },
  },
  emit: ["update:filterParams"],
  setup(props, { emit }) {
    const t = useI18n();

    const modelInfoOptions = computed(
      () => props.options?.containerTypeList?.map(({ name, id }) => ({ label: name, value: String(id) })) || []
    );

    const modelInfoIds = computed(() => modelInfoOptions.value.map((v) => v.value).join(""));

    const formItems = computed(() => [
      {
        name: "hostCellCode",
        type: formItemTypes.EL_INPUT,
        filterable: true,
        clearable: true,
        placeholder: t("lang.ark.fed.areaCode"),
        value: "",
        // suffixIcon: "el-icon-search",
      },
      {
        name: "areaName",
        type: formItemTypes.EL_INPUT,
        filterable: true,
        clearable: true,
        placeholder: t("lang.ark.fed.areaName"),
        value: "",
        // suffixIcon: "el-icon-search",
      },
    ]);

    const handleGroupChange = ({ formData }) => {
      emit("change", Object.fromEntries(Object.entries(formData).filter(([, val]) => val)));
    };

    return { formItems, modelInfoOptions, modelInfoIds, handleGroupChange };
  },
});
</script>

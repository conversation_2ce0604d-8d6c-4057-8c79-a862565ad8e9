<template>
  <div class="tw-flex tw-flex-col tw-relative tw--mt-4">
    <!-- 搜索框 -->
    <div class="tw-flex tw-justify-end tw-gap-2 tw-my-4">
      <QueueFilter>
        <template #suffix>
          <ButtonEnhanced type="primary" >
            {{ $t("lang.ark.fed.addNew") }}
          </ButtonEnhanced>
        </template>
      </QueueFilter>
    </div>
    <!-- 表格数据 -->
  </div>
</template>
<script>
import { defineComponent } from "vue";
import QueueFilter from "./queue-filter.vue";
export default defineComponent({
  name: "QueueList",
  components: {
    QueueFilter,
  },
});
</script>

